<div class="crm-home-container">
  <!-- <PERSON><PERSON><PERSON><PERSON> principal -->
  <div class="main-header">
    <div class="header-content">
      <span class="header-icon">🎯</span>
      <h1 class="main-title">Assistente de Vendas</h1>
      <span class="subtitle">CardápioTech - Especialista em objeções</span>
    </div>
  </div>

  <!-- Estado de carregamento -->
  <div class="loading-state" *ngIf="carregandoLead">
    <div class="loading-spinner">
      <i class="fa fa-spinner fa-spin"></i>
      <p>Buscando lead para @{{ username }}...</p>
    </div>
  </div>

  <!-- Mensagem temporária: lead não encontrado (será redirecionado automaticamente) -->
  <div class="loading-state" *ngIf="!carregandoLead && !leadEncontrado && username">
    <div class="loading-spinner">
      <i class="fa fa-info-circle"></i>
      <p>Lead não encontrado para @{{ username }}. Redirecionando...</p>
    </div>
  </div>

  <!-- Card detalhado do lead -->
  <div class="lead-detailed-card" *ngIf="!carregandoLead && leadEncontrado && dadosLeadAtual">
    
    <!-- Cabeçalho do Lead -->
    <div class="lead-header">
      <div class="lead-avatar-section">
        <div class="lead-avatar">
          <i class="fa fa-user"></i>
        </div>
        <div class="score-badge" [ngStyle]="{'background-color': getCorDoScore(dadosLeadAtual?.scoreLead)}">
          {{ formatarScore(dadosLeadAtual?.scoreLead) }}
        </div>
      </div>

      <div class="lead-title-section">
        <h1 class="lead-name">{{ dadosLeadAtual?.nome || 'Lead sem nome' }}</h1>
        <h2 class="lead-company" *ngIf="dadosLeadAtual?.empresa">{{ dadosLeadAtual?.empresa }}</h2>
        <div class="lead-meta">
          <span class="stage-badge" [ngClass]="dadosLeadAtual?.etapaFunil?.toLowerCase() || ''">
            {{ dadosLeadAtual?.etapaFunil || 'Indefinida' }}
          </span>
          <span class="origin-badge" *ngIf="dadosLeadAtual?.origemLead">
            {{ dadosLeadAtual?.origemLead }}
          </span>
          <span class="segment-badge" *ngIf="dadosLeadAtual?.segmento">
            {{ dadosLeadAtual?.segmento }}
          </span>
        </div>
      </div>

      <!-- Ações rápidas -->
      <div class="lead-quick-actions">
        <a class="action-btn phone-btn" 
           *ngIf="dadosLeadAtual?.telefone" 
           [href]="'tel:' + dadosLeadAtual?.telefone" 
           title="Ligar para {{ dadosLeadAtual?.telefone }}">
          <i class="fa fa-phone"></i>
          <span>Ligar</span>
        </a>
        <a class="action-btn whatsapp-btn" 
           *ngIf="dadosLeadAtual?.telefone" 
           [href]="getWhatsAppUrl(dadosLeadAtual?.telefone)" 
           target="_blank"
           title="WhatsApp {{ dadosLeadAtual?.telefone }}">
          <i class="fa fa-whatsapp"></i>
          <span>WhatsApp</span>
        </a>
        <a class="action-btn email-btn" 
           *ngIf="dadosLeadAtual?.email" 
           [href]="'mailto:' + dadosLeadAtual?.email" 
           title="Enviar email para {{ dadosLeadAtual?.email }}">
          <i class="fa fa-envelope"></i>
          <span>Email</span>
        </a>
      </div>
    </div>

    <!-- Informações de Contato -->
    <div class="lead-contact-section">
      <h3 class="section-title">
        <i class="fa fa-address-card"></i>
        Informações de Contato
      </h3>
      
      <div class="contact-grid">
        <div class="contact-item" *ngIf="dadosLeadAtual?.telefone">
          <label>Telefone:</label>
          <div class="contact-value">
            <i class="fa fa-phone"></i>
            <span>{{ dadosLeadAtual?.telefone }}</span>
            <a [href]="'tel:' + dadosLeadAtual?.telefone" class="contact-action" title="Ligar">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.email">
          <label>Email:</label>
          <div class="contact-value">
            <i class="fa fa-envelope"></i>
            <span>{{ dadosLeadAtual?.email }}</span>
            <a [href]="'mailto:' + dadosLeadAtual?.email" class="contact-action" title="Enviar email">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.instagram">
          <label>Instagram:</label>
          <div class="contact-value">
            <i class="fa fa-instagram"></i>
            <span>{{ dadosLeadAtual?.instagram }}</span>
            <a [href]="getInstagramUrl(dadosLeadAtual?.instagram)" 
               target="_blank" 
               class="contact-action" 
               title="Abrir perfil no Instagram">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.site">
          <label>Website:</label>
          <div class="contact-value">
            <i class="fa fa-globe"></i>
            <a [href]="getWebsiteUrl(dadosLeadAtual?.site)" target="_blank" class="website-link">
              {{ dadosLeadAtual?.site }}
            </a>
            <a [href]="getWebsiteUrl(dadosLeadAtual?.site)" 
               target="_blank" 
               class="contact-action" 
               title="Abrir website">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.linkedin">
          <label>LinkedIn:</label>
          <div class="contact-value">
            <i class="fa fa-linkedin"></i>
            <a [href]="dadosLeadAtual?.linkedin" target="_blank" class="linkedin-link">
              {{ dadosLeadAtual?.linkedin }}
            </a>
            <a [href]="dadosLeadAtual?.linkedin" 
               target="_blank" 
               class="contact-action" 
               title="Abrir perfil no LinkedIn">
              <i class="fa fa-external-link"></i>
            </a>
          </div>
        </div>

        <div class="contact-item" *ngIf="dadosLeadAtual?.localizacao">
          <label>Localização:</label>
          <div class="contact-value">
            <i class="fa fa-map-marker"></i>
            <span>{{ dadosLeadAtual?.localizacao }}</span>
          </div>
        </div>
      </div>
    </div>

    <!-- Links do Lead -->
    <div class="lead-links-section" *ngIf="dadosLeadAtual?.links && dadosLeadAtual?.links.length > 0">
      <h3 class="section-title">
        <i class="fa fa-link"></i>
        Links Importantes
      </h3>

      <div class="links-compact-list">
        <div class="link-compact-item" *ngFor="let link of dadosLeadAtual?.links">
          <div class="link-icon" [style.color]="getLinkColor(link.tipo)">
            <i [ngClass]="getLinkIcon(link.tipo)"></i>
          </div>
          <div class="link-info">
            <div class="link-type">{{ link.tipo }}</div>
            <a [href]="getLinkUrl(link)"
               target="_blank"
               class="link-url"
               [title]="link.descricao || 'Abrir ' + link.tipo">
              {{ link.url }}
            </a>
            <div class="link-description" *ngIf="link.descricao">{{ link.descricao }}</div>
          </div>
          <a [href]="getLinkUrl(link)"
             target="_blank"
             class="link-external-btn"
             [title]="'Abrir ' + link.tipo">
            <i class="fas fa-link"></i>
          </a>
        </div>
      </div>
    </div>

    <!-- Informações de Negócio -->
    <div class="lead-business-section">
      <h3 class="section-title">
        <i class="fa fa-briefcase"></i>
        Informações de Negócio
      </h3>
      
      <div class="business-grid">
        <div class="business-item" *ngIf="dadosLeadAtual?.dataPrimeiroContato">
          <label>Primeiro Contato:</label>
          <span>{{ dadosLeadAtual?.dataPrimeiroContato }}</span>
        </div>

        <div class="business-item" *ngIf="dadosLeadAtual?.ultimaInteracao">
          <label>Última Interação:</label>
          <span>{{ dadosLeadAtual?.ultimaInteracao }}</span>
        </div>

        <div class="business-item" *ngIf="dadosLeadAtual?.proximoFollowUp">
          <label>Próximo Follow-up:</label>
          <span class="follow-up-date">{{ dadosLeadAtual?.proximoFollowUp }}</span>
        </div>

        <div class="business-item" *ngIf="dadosLeadAtual?.tamanhoEmpresa">
          <label>Tamanho da Empresa:</label>
          <span>{{ dadosLeadAtual?.tamanhoEmpresa }}</span>
        </div>

        <div class="business-item" *ngIf="dadosLeadAtual?.interessesProdutos && dadosLeadAtual?.interessesProdutos.length > 0">
          <label>Interesses:</label>
          <div class="interests-list">
            <span class="interest-tag" *ngFor="let interesse of dadosLeadAtual?.interessesProdutos">
              {{ interesse }}
            </span>
          </div>
        </div>
      </div>
    </div>

    <!-- Observações -->
    <div class="lead-notes-section" *ngIf="dadosLeadAtual?.observacoes || dadosLeadAtual?.notas || dadosLeadAtual?.historicoPropostas">
      <h3 class="section-title">
        <i class="fa fa-sticky-note"></i>
        Observações e Histórico
      </h3>
      
      <div class="notes-content">
        <div class="note-item" *ngIf="dadosLeadAtual?.observacoes">
          <label>Observações de Vendas:</label>
          <p>{{ dadosLeadAtual?.observacoes }}</p>
        </div>

        <div class="note-item" *ngIf="dadosLeadAtual?.notas">
          <label>Dados do Instagram:</label>
          <p class="instagram-notes">{{ dadosLeadAtual?.notas }}</p>
        </div>

        <div class="note-item" *ngIf="dadosLeadAtual?.historicoPropostas">
          <label>Histórico de Propostas:</label>
          <p>{{ dadosLeadAtual?.historicoPropostas }}</p>
        </div>
      </div>
    </div>

  </div>
</div>
