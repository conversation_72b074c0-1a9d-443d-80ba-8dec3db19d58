import { CrmEmpresa } from './CrmEmpresa';
import { LeadLink, TipoLinkLead } from './LeadLink';

export enum EtapaFunilLead {
  Prospecção = 'Prospecção',
  Qualificação = 'Qualificação',
  Objeção = 'Objeção',
  Fechamento = 'Fechamento',
  Ganho = 'Ganho',
  Perdido = 'Perdido'
}

export enum OrigemLead {
  Instagram = 'Instagram',
  SiteLandingPage = 'Site/Landing Page',
  WhatsappDireto = 'WhatsApp Direto',
  Indicacao = 'Indicação',
  EventoFeira = 'Evento/Feira',
  Outros = 'Outros'
}

export enum SegmentoLead {
  Restaurante = 'Restaurante',
  Pizzaria = 'Pizzaria',
  Lanchonete = 'Lanchonete',
  Hamburgueria = 'Hamburgueria',
  Confeitaria = 'Confeitaria/Doceria',
  Bar = 'Bar/Boteco',
  FoodTruck = 'Food Truck',
  Outros = 'Outros'
}

export interface InstagramData {
  bio?: string;
  followers?: number;
  following?: number;
  accountType?: 'Pessoal' | 'Business';
  businessCategory?: string;
  location?: string;
  website?: string;
}

export interface RapportGerado {
  tipoNegocio?: string;
  pontosDor?: string[]; // itens como "filas", "cardápio físico" etc.
  sugestoesAbordagem?: string[];
  pontosInteresse?: string[];
}

export default class Lead {
  id?: number;
  crmEmpresaId: number; // Vinculação obrigatória com CrmEmpresa
  nomeResponsavel: string;
  empresa: string;
  telefone: string;
  instagramHandle?: string;
  linkInsta?: string; // URL/link externo da bio do Instagram
  // Bio extraída diretamente do perfil do Instagram (campo duplicado para fácil acesso)
  bioInsta?: string;

  etapa: EtapaFunilLead = EtapaFunilLead.Prospecção;
  score: number = 0; // 0-100
  origem: OrigemLead = OrigemLead.Instagram;
  segmento?: SegmentoLead;

  // Datas
  dataCriacao: Date = new Date();
  dataUltimaInteracao?: Date;
  dataProximoFollowup?: Date;
  dataFechamento?: Date;

  // Dados de negócio
  valorPotencial?: number;
  vendedorId?: number;
  motivoPerda?: string;

  // Dados do Instagram (quando origem = Instagram)
  instagramData?: InstagramData;
  avatarUrl?: string; // URL da foto de perfil do Instagram

  // Website da empresa (compatibilidade com frontend)
  website?: string;

  // Dados gerados por IA
  rapport?: RapportGerado;
  relatorioIaJson?: string; // JSON com dados completos da análise de IA

  // Observações livres
  notas?: string;
  observacoes?: string; // Observações específicas sobre vendas/negociação

  // Links do lead
  links?: LeadLink[];

  // Campos de auditoria
  createdAt?: Date;
  updatedAt?: Date;

  crmEmpresa?: CrmEmpresa;

  constructor(
    crmEmpresaId: number,
    nomeResponsavel: string,
    empresa: string,
    telefone: string,
    instagramHandle?: string,
    bioInsta?: string,
    origem: OrigemLead = OrigemLead.Instagram
  ) {
    this.crmEmpresaId = crmEmpresaId;
    this.nomeResponsavel = nomeResponsavel?.trim();
    this.empresa = empresa?.trim();
    this.telefone = telefone?.replace(/\D/g, '');
    this.instagramHandle = instagramHandle?.replace('@', '').trim();
    if (bioInsta) this.bioInsta = bioInsta;
    this.origem = origem;
  }

  // Métodos de negócio
  avancarEtapa(): boolean {
    const ordem: EtapaFunilLead[] = [
      EtapaFunilLead.Prospecção,
      EtapaFunilLead.Qualificação,
      EtapaFunilLead.Objeção,
      EtapaFunilLead.Fechamento,
      EtapaFunilLead.Ganho
    ];
    const idx = ordem.indexOf(this.etapa);
    if (idx >= 0 && idx < ordem.length - 1) {
      this.etapa = ordem[idx + 1];
      this.dataUltimaInteracao = new Date();
      return true;
    }
    return false;
  }

  retrocederEtapa(): boolean {
    const ordem: EtapaFunilLead[] = [
      EtapaFunilLead.Prospecção,
      EtapaFunilLead.Qualificação,
      EtapaFunilLead.Objeção,
      EtapaFunilLead.Fechamento,
      EtapaFunilLead.Ganho
    ];
    const idx = ordem.indexOf(this.etapa);
    if (idx > 0) {
      this.etapa = ordem[idx - 1];
      this.dataUltimaInteracao = new Date();
      return true;
    }
    return false;
  }

  marcarComoGanho(valorFechamento?: number): void {
    this.etapa = EtapaFunilLead.Ganho;
    this.dataFechamento = new Date();
    this.dataUltimaInteracao = new Date();
    if (valorFechamento) {
      this.valorPotencial = valorFechamento;
    }
    this.score = 100;
  }

  marcarComoPerdido(motivo?: string): void {
    this.etapa = EtapaFunilLead.Perdido;
    this.dataFechamento = new Date();
    this.dataUltimaInteracao = new Date();
    this.motivoPerda = motivo;
    this.score = 0;
  }

  // Classificações
  getScoreClassificacao(): 'Alto' | 'Médio' | 'Baixo' | 'Sem' {
    if (this.score >= 80) return 'Alto';
    if (this.score >= 50) return 'Médio';
    if (this.score > 0) return 'Baixo';
    return 'Sem';
  }

  getCorScore(): string {
    if (this.score >= 80) return '#28a745'; // Verde
    if (this.score >= 50) return '#ffc107'; // Amarelo
    if (this.score > 0) return '#dc3545';   // Vermelho
    return '#6c757d'; // Cinza
  }

  getIconeEtapa(): string {
    const icones = {
      'Prospecção': 'fa-eye',
      'Qualificação': 'fa-search',
      'Objeção': 'fa-exclamation-triangle',
      'Fechamento': 'fa-handshake',
      'Ganho': 'fa-check-circle',
      'Perdido': 'fa-times-circle'
    };
    return icones[this.etapa] || 'fa-circle';
  }

  // Validações
  isValid(): boolean {
    return !!(this.crmEmpresaId &&
             this.nomeResponsavel?.trim() &&
             this.empresa?.trim() &&
             this.telefone?.trim());
  }

  isFechado(): boolean {
    return this.etapa === EtapaFunilLead.Ganho || this.etapa === EtapaFunilLead.Perdido;
  }

  isAtrasado(): boolean {
    if (!this.dataProximoFollowup) return false;
    return new Date() > this.dataProximoFollowup;
  }

  // Formatações
  formatarTelefone(): string {
    if (!this.telefone) return '';
    const telefone = this.telefone.replace(/\D/g, '');
    if (telefone.length === 11) {
      return telefone.replace(/^(\d{2})(\d{5})(\d{4})/, '($1) $2-$3');
    } else if (telefone.length === 10) {
      return telefone.replace(/^(\d{2})(\d{4})(\d{4})/, '($1) $2-$3');
    }
    return this.telefone;
  }

  formatarInstagram(): string {
    if (!this.instagramHandle) return '';
    return `@${this.instagramHandle}`;
  }

  getLinkBioInstagram(): string {
    return this.linkInsta || '';
  }

  getLinkPerfilInstagram(): string {
    if (this.instagramHandle) return `https://instagram.com/${this.instagramHandle}`;
    return '';
  }

  formatarValorPotencial(): string {
    if (!this.valorPotencial) return 'N/A';
    return this.valorPotencial.toLocaleString('pt-BR', {
      style: 'currency',
      currency: 'BRL'
    });
  }

  // Resumo para listagens
  getResumo(): string {
    return `${this.nomeResponsavel} - ${this.empresa} (${this.etapa})`;
  }

  // Métodos para extrair links específicos por tipo
  getLinkPorTipo(tipo: string): string | null {
    if (!this.links || this.links.length === 0) return null;
    const link = this.links.find(l => l.tipo === tipo && l.ativo);
    return link ? link.url : null;
  }

  // Obter objeto LeadLink por tipo
  getLeadLinkPorTipo(tipo: TipoLinkLead): LeadLink | undefined {
    return this.links?.find(link => link.tipo === tipo && link.ativo);
  }

  getLinkInstagram(): string | null {
    return this.getLinkPorTipo('Instagram') || this.linkInsta || this.getLinkPerfilInstagram();
  }

  getLinkSite(): string | null {
    return this.getLinkPorTipo('Site') || this.website || this.instagramData?.website;
  }

  getLinkConcorrente(): string | null {
    return this.getLinkPorTipo('Concorrente');
  }

  getLinkIfood(): string | null {
    return this.getLinkPorTipo('Ifood');
  }

  getLinkWhatsApp(): string | null {
    return this.getLinkPorTipo('WhatsApp');
  }

  getLinkLocalizacao(): string | null {
    return this.getLinkPorTipo('Localização');
  }

  // ===== MÉTODOS PARA GERENCIAR LINKS =====

  // Adicionar um novo link
  adicionarLink(tipo: TipoLinkLead, url: string, descricao?: string): LeadLink {
    if (!this.links) this.links = [];

    // Verificar se já existe um link do mesmo tipo
    const linkExistente = this.links.find(link => link.tipo === tipo);
    if (linkExistente) {
      linkExistente.url = url;
      linkExistente.descricao = descricao;
      linkExistente.ativo = true;
      return linkExistente;
    }

    const novoLink = new LeadLink(this.id || 0, tipo, url, descricao, this.links.length);
    this.links.push(novoLink);
    this.sincronizarLinkInsta();
    return novoLink;
  }

  // Remover link por tipo
  removerLink(tipo: TipoLinkLead): boolean {
    if (!this.links) return false;

    const index = this.links.findIndex(link => link.tipo === tipo);
    if (index >= 0) {
      this.links.splice(index, 1);
      this.sincronizarLinkInsta();
      return true;
    }
    return false;
  }



  // Obter todos os links ativos
  getLinksAtivos(): LeadLink[] {
    return this.links?.filter(link => link.ativo) || [];
  }

  // Obter links por categoria
  getLinksPorCategoria(): { [categoria: string]: LeadLink[] } {
    const links = this.getLinksAtivos();
    return {
      'Contato': links.filter(l => ['WhatsApp', 'Instagram'].includes(l.tipo)),
      'Negócio': links.filter(l => ['Ifood', 'Site do Cardápio', 'Reservas'].includes(l.tipo)),
      'Informações': links.filter(l => ['Site', 'Localização'].includes(l.tipo))
    };
  }

  // Sincronizar campo linkInsta com array de links (compatibilidade)
  sincronizarLinkInsta(): void {
    const linkInstagram = this.getLeadLinkPorTipo(TipoLinkLead.Instagram);
    this.linkInsta = linkInstagram?.url || '';
  }

  // Inicializar links a partir do campo linkInsta existente
  inicializarLinksDoLinkInsta(): void {
    if (this.linkInsta && !this.getLeadLinkPorTipo(TipoLinkLead.Instagram)) {
      this.adicionarLink(TipoLinkLead.Instagram, this.linkInsta, 'Link da bio do Instagram');
    }
  }

  // Validar todos os links
  validarLinks(): { validos: LeadLink[], invalidos: LeadLink[] } {
    const links = this.getLinksAtivos();
    return {
      validos: links.filter(link => link.isUrlValida()),
      invalidos: links.filter(link => !link.isUrlValida())
    };
  }

  // Obter link do WhatsApp formatado
  getWhatsAppUrl(): string {
    const linkWhatsApp = this.getLeadLinkPorTipo(TipoLinkLead.Whatsapp);
    if (linkWhatsApp) return linkWhatsApp.getUrlFormatada();

    // Fallback para o telefone
    if (this.telefone) {
      const numeroLimpo = this.telefone.replace(/\D/g, '');
      return `https://wa.me/55${numeroLimpo}`;
    }
    return '';
  }

  // Obter link de localização
  getLocalizacaoUrl(): string {
    const linkLocalizacao = this.getLeadLinkPorTipo(TipoLinkLead.Localizacao);
    return linkLocalizacao?.getUrlFormatada() || '';
  }
}
